<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: Number,
    default: 1
  },
  min: {
    type: Number,
    default: 1
  },
  max: {
    type: Number,
    default: null
  },
  label: {
    type: String,
    default: 'Aantal:'
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue']);

const localValue = ref(props.modelValue);

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  localValue.value = newValue;
});

// Watch for local changes and emit them
watch(localValue, (newValue) => {
  // Ensure value is within bounds
  if (newValue < props.min) {
    localValue.value = props.min;
    return;
  }
  if (props.max && newValue > props.max) {
    localValue.value = props.max;
    return;
  }
  emit('update:modelValue', newValue);
});

function increment() {
  if (props.disabled) return;
  if (props.max && localValue.value >= props.max) return;
  localValue.value++;
}

function decrement() {
  if (props.disabled) return;
  if (localValue.value <= props.min) return;
  localValue.value--;
}

function handleInput(event) {
  const value = parseInt(event.target.value) || props.min;
  localValue.value = value;
}
</script>

<template>
  <div class="quantity-section">
    <label v-if="label" for="quantity" class="quantity-label">{{ label }}</label>
    <div class="quantity-controls">
      <button 
        type="button" 
        class="quantity-btn" 
        @click="decrement"
        :disabled="disabled || localValue <= min"
        aria-label="Decrease quantity"
      >
        -
      </button>
      <input 
        id="quantity"
        :value="localValue"
        @input="handleInput"
        type="number" 
        :min="min" 
        :max="max"
        :disabled="disabled"
        class="quantity-input"
        aria-label="Quantity"
      />
      <button 
        type="button" 
        class="quantity-btn" 
        @click="increment"
        :disabled="disabled || (max && localValue >= max)"
        aria-label="Increase quantity"
      >
        +
      </button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.quantity-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.quantity-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
  margin: 0;
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 1px solid #ced4da;
  border-radius: 4px;
  overflow: hidden;
}

.quantity-btn {
  background-color: #f8f9fa;
  border: none;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  transition: background-color 0.2s;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    background-color: #e9ecef;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:first-child {
    border-right: 1px solid #ced4da;
  }

  &:last-child {
    border-left: 1px solid #ced4da;
  }
}

.quantity-input {
  border: none;
  padding: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  width: 60px;
  height: 36px;
  outline: none;
  background-color: white;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  &[type=number] {
    -moz-appearance: textfield;
  }
}

@media (max-width: 768px) {
  .quantity-section {
    justify-content: center;
  }
}
</style>
