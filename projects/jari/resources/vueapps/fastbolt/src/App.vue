<script setup>
import { onMounted, ref, computed } from 'vue';
import axios from 'axios';
import Loader from "./components/Loader.vue";
import ProductModal from "./components/ProductModal.vue";

const container = window.productContainer;
const basketUrl = window.basketUrl;

const products = ref([]);
const loading = ref(true);
const hovered = ref();
const selectedProduct = ref(null);
const isModalOpen = ref(false);

const productLookup = computed(() => {
  const lookup = {};
  products.value.forEach(product => {
    const diameter = product.options.find(o => o.code === 'diameter')?.value;
    const length = product.options.find(o => o.code === 'length')?.value;

    if (diameter && length) {
      lookup[`${diameter}-${length}`] = product;
    }
  });
  return lookup;
});

const diameters = computed(() => {
  return getUniqueSortedOptions(products.value, 'diameter');
});

const lengths = computed(() => {
  return getUniqueSortedOptions(products.value, 'length');
});

const sizesAvailable = computed(() => diameters.value.length > 0 && lengths.value.length > 0);

function getUniqueSortedOptions(products, optionCode) {
  const options = products.flatMap(p =>
      p.options.filter(o => o.code === optionCode)
  );

  const values = options
      .map(o => o.value)
      .filter(Boolean)
      .map(Number);

  return [...new Set(values)].sort((a, b) => a - b);
};

function isHighlighted(diameter, length) {
  if (!hovered.value) return false;

  return (hovered.value.diameter === diameter && hovered.value.length >= length)
      || (hovered.value.length === length && hovered.value.diameter >= diameter);
};

function getProduct(diameter, length) {
  return productLookup.value[`${diameter}-${length}`];
}

function handleClick(diameter, length) {
  const product = getProduct(diameter, length);
  if (product) {
    selectedProduct.value = product;
    isModalOpen.value = true;
  }
}

function closeModal() {
  isModalOpen.value = false;
  selectedProduct.value = null;
}

async function handleAddToBasket(data) {
  try {
    const product = data.product;
    const quantity = data.quantity;
    const dataUrl = `add=basket&size[${product.id}]=${quantity}`;
    await axios.post(basketUrl, dataUrl);
    window.location = basketUrl;
  } catch (error) {
    console.error('Error adding to basket:', error);
  }
}

async function loadProducts() {
  try {
    const response = await axios.get(`?action=getProductsAjax&containerId=${container.id}`);
    products.value = response.data;
  } catch (error) {
    console.error('Error loading products:', error);
  } finally {
    loading.value = false;
  }
}

onMounted(loadProducts);
</script>

<template>
  <Loader v-if="loading" />

  <table v-else-if="sizesAvailable" class="product-grid">
    <tbody>
    <tr>
      <td class="header">I/ø</td>
      <td
          v-for="length in lengths"
          :key="length"
          class="header"
      >
        {{ length }}
      </td>
    </tr>
    <tr v-for="diameter in diameters" :key="diameter">
      <td class="header">{{ diameter }}</td>
      <td
          v-for="length in lengths"
          :key="`${diameter}-${length}`"
          :class="{
            'cell': true,
            'has-product': getProduct(diameter, length),
            'highlight': isHighlighted(diameter, length)
          }"
          @click="handleClick(diameter, length)"
          @mouseenter="hovered = { diameter, length }"
          @mouseleave="hovered = null"
      >
          <span v-if="getProduct(diameter, length)" class="product-code">
            {{ diameter }} × {{ length }}
          </span>
      </td>
    </tr>
    </tbody>
  </table>
  <div v-else>Geen maten beschikbaar</div>

  <ProductModal
    :product="selectedProduct"
    :container="container"
    :is-open="isModalOpen"
    @close="closeModal"
    @add-to-basket="handleAddToBasket"
  />
</template>

<style scoped lang="scss">
tbody {
  border: none !important;
}

.product-grid {
  display: block;
  max-height: 400px;
  overflow: auto;
  width: 100%;

  &::-webkit-scrollbar {
    height: 8px;
    width: 8px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: var(--color-primary);
  }

  tbody {
    display: table;
    width: 100%;
    border-collapse: collapse;
  }

  tr {
    display: table-row;
  }

  td {
    display: table-cell;
    text-align: center;
  }
}

.header {
  background-color: var(--color-primary);
  color: var(--color-white);
  position: sticky;
  z-index: 1;

  &:first-child {
    left: 0;
    z-index: 2;
  }
}

tr:first-child .header {
  top: 0;
  z-index: 3;

  &:first-child {
    z-index: 4;
  }
}

.cell {
  background-color: var(--color-gray);

  &.has-product {
    background-color: var(--color-light-blue);
    cursor: pointer;

    &.highlight {
      background-color: var(--color-primary);
    }

    &:hover {
      background-color: var(--color-bg-danger);
      color: white;

      .product-code {
        color: white;
      }
    }
  }

  &.highlight {
    background-color: var(--color-medium-blue);
  }
}

.product-code {
  font-size: 12px;
  color: var(--color-primary);
}
</style>